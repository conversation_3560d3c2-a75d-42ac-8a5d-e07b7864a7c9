/**
 * consent router
 */

export default {
  routes: [
    // Obtener todos los consentimientos (solo admin)
    {
      method: "GET",
      path: "/consents",
      handler: "consent.find",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Obtener un consentimiento específico
    {
      method: "GET",
      path: "/consents/:id",
      handler: "consent.findOne",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Crear un nuevo consentimiento
    {
      method: "POST",
      path: "/consents",
      handler: "consent.create",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Aceptar términos y condiciones (crear consentimiento)
    {
      method: "POST",
      path: "/consents/accept",
      handler: "consent.acceptTerms",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Verificar si el usuario tiene consentimiento activo
    {
      method: "GET",
      path: "/consents/check",
      handler: "consent.checkConsent",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Exportar consentimientos (solo admin)
    {
      method: "GET",
      path: "/consents/export",
      handler: "consent.export",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
  ],
};
