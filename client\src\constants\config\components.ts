import { CategoryType } from "../categoriesList";

export interface Component {
  name: string;
  title: string;
  url: string;
  categories: CategoryType[];
  keywords: string[];
}

export const components: Component[] = [
  // Dashboard components
  {
    name: "Dashboard",
    title: "Panel Principal",
    url: `/dashboard`,
    categories: ["dashboard"],
    keywords: ["dashboard", "inicio", "home", "panel", "principal", "resumen"],
  },

  // Principales rutas del sistema
  {
    name: "Noticias",
    title: "Noticias y Comunicados",
    url: `/news`,
    categories: ["apps"],
    keywords: [
      "news",
      "noticias",
      "comunicados",
      "anuncios",
      "información",
      "avisos",
    ],
  },
  {
    name: "Negocio<PERSON>",
    title: "Directorio de Negocios",
    url: `/bussiness`,
    categories: ["apps"],
    keywords: [
      "business",
      "negocios",
      "comercio",
      "tiendas",
      "servicios",
      "directorio",
    ],
  },
  {
    name: "<PERSON><PERSON><PERSON> y Venta",
    title: "Inmuebles en Alquiler y Venta",
    url: `/houses`,
    categories: ["apps"],
    keywords: [
      "inmuebles",
      "casas",
      "alquiler",
      "venta",
      "propiedades",
      "arriendo",
    ],
  },
  {
    name: "Reservas",
    title: "Reserva de Áreas Comunes",
    url: `/reservations`,
    categories: ["apps"],
    keywords: [
      "reservas",
      "áreas comunes",
      "salón",
      "piscina",
      "bbq",
      "gimnasio",
      "espacios",
    ],
  },

  // Encuestas
  {
    name: "Mis Encuestas",
    title: "Mis Encuestas y Votaciones",
    url: `/surveys`,
    categories: ["apps"],
    keywords: [
      "encuestas",
      "votaciones",
      "opiniones",
      "participación",
      "consultas",
    ],
  },
  {
    name: "Gestor de Encuestas",
    title: "Administración de Encuestas",
    url: `/admin/surveys`,
    categories: ["apps"],
    keywords: [
      "gestión encuestas",
      "crear encuesta",
      "administrar votaciones",
      "resultados",
      "estadísticas",
    ],
  },
  {
    name: "Pagos",
    title: "Gestión de Pagos",
    url: `/admin/payments`,
    categories: ["admin"],
    keywords: [
      "pagos",
      "facturas",
      "cobros",
      "pagos",
      "administración",
      "historial pagos",
      "transacciones",
    ],
  },

  // Administración
  {
    name: "Configuración",
    title: "Configuración del Sistema",
    url: `/admin/settings`,
    categories: ["admin"],
    keywords: [
      "configuración",
      "ajustes",
      "sistema",
      "parámetros",
      "opciones",
      "preferencias",
    ],
  },
  {
    name: "Usuarios",
    title: "Gestión de Usuarios",
    url: `/admin/users`,
    categories: ["admin"],
    keywords: [
      "usuarios",
      "residentes",
      "propietarios",
      "arrendatarios",
      "gestión usuarios",
    ],
  },
  {
    name: "Gestion alquiler y ventas",
    title: "Gestión de Inmuebles",
    url: `/admin/houses`,
    categories: ["admin"],
    keywords: [
      "gestión inmuebles",
      "administrar propiedades",
      "control alquiler",
      "ventas",
      "inmobiliaria",
    ],
  },
  {
    name: "Gestor de Usuarios",
    title: "Gestión de Mascotas",
    url: `/admin/pets`,
    categories: ["admin"],
    keywords: [
      "mascotas",
      "registro mascotas",
      "control animal",
      "pets",
      "administración mascotas",
    ],
  },
  {
    name: "Gestión de Reservas",
    title: "Administración de Reservas",
    url: `/admin/reservations`,
    categories: ["admin"],
    keywords: [
      "gestión reservas",
      "control áreas",
      "administrar espacios",
      "calendario reservas",
    ],
  },
  {
    name: "Negocios",
    title: "Gestión de Negocios",
    url: `/admin/business`,
    categories: ["admin"],
    keywords: [
      "negocios",
      "gestión negocios",
      "administrar emprendimientos",
      "emprendimiento",
      "gestión emprendimiento",
    ],
  },
  {
    name: "Consentimientos",
    title: "Gestión de Consentimientos",
    url: `/admin/consents`,
    categories: ["admin"],
    keywords: [
      "consentimientos",
      "protección datos",
      "GDPR",
      "privacidad",
      "términos condiciones",
      "gestión consentimientos",
      "auditoría",
      "cumplimiento legal",
    ],
  },

  // Mascotas
  {
    name: "Mascotas",
    title: "Registro de Mascotas",
    url: `/pets`,
    categories: ["pets"],
    keywords: [
      "mascotas",
      "registro",
      "animales",
      "pets",
      "mascota",
      "veterinaria",
    ],
  },

  // Vigilancia
  {
    name: "Vehículos",
    title: "Control de Vehículos",
    url: `/vigilance/vehicles`,
    categories: ["vigilance"],
    keywords: [
      "vehículos",
      "parqueadero",
      "control vehicular",
      "parking",
      "estacionamiento",
      "vigilancia",
    ],
  },

  // Formatos
  {
    name: "Formatos",
    title: "Documentos y Formatos",
    url: `/formats`,
    categories: ["formats"],
    keywords: [
      "formatos",
      "documentos",
      "plantillas",
      "formularios",
      "solicitudes",
      "formatos oficiales",
    ],
  },

  // Perfil
  {
    name: "Información Personal",
    title: "Datos Personales",
    url: `/profile/personal-info`,
    categories: ["profile"],
    keywords: [
      "perfil",
      "datos personales",
      "información",
      "contacto",
      "identidad",
    ],
  },
  {
    name: "Dependientes",
    title: "Gestión de Dependientes",
    url: `/profile/dependents`,
    categories: ["profile"],
    keywords: [
      "dependientes",
      "familiares",
      "residentes adicionales",
      "grupo familiar",
    ],
  },
  {
    name: "Mascotas",
    title: "Mis Mascotas",
    url: `/profile/pets`,
    categories: ["profile"],
    keywords: ["mascotas", "mis mascotas", "registro mascota", "animales"],
  },
  {
    name: "Vehículos",
    title: "Mis Vehículos",
    url: `/profile/vehicles`,
    categories: ["profile"],
    keywords: ["vehículos", "mis vehículos", "carros", "motos", "parqueadero"],
  },
  {
    name: "Configuración de Seguridad",
    title: "Seguridad de la Cuenta",
    url: `/profile/security-settings`,
    categories: ["profile"],
    keywords: [
      "seguridad",
      "contraseña",
      "privacidad",
      "autenticación",
      "configuración seguridad",
    ],
  },
  {
    name: "Notificaciones",
    title: "Centro de Notificaciones",
    url: `/profile/notifications`,
    categories: ["profile"],
    keywords: [
      "notificaciones",
      "alertas",
      "avisos",
      "mensajes",
      "comunicaciones",
    ],
  },
  {
    name: "Pagos",
    title: "Historial de Pagos",
    url: `/profile/payments`,
    categories: ["profile"],
    keywords: [
      "pagos",
      "cuotas",
      "facturas",
      "administración",
      "historial pagos",
      "transacciones",
    ],
  },

  // Error pages
  {
    name: "Server error",
    title: "Error del Servidor",
    url: `/server-error`,
    categories: ["error"],
    keywords: ["error", "servidor", "fallo", "sistema", "500"],
  },
  {
    name: "Error 404",
    title: "Página no Encontrada",
    url: `/404`,
    categories: ["error"],
    keywords: [
      "404",
      "no encontrado",
      "página inexistente",
      "error",
      "ruta inválida",
    ],
  },
];
