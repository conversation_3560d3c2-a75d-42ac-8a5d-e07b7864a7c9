import {
  BaiduOutlined,
  <PERSON>Outlined,
  CalculatorOutlined,
  CalendarOutlined,
  CarOutlined,
  CommentOutlined,
  ControlOutlined,
  DollarCircleFilled,
  ExclamationCircleOutlined,
  FileTextOutlined,
  FormOutlined,
  HistoryOutlined,
  HomeOutlined,
  IdcardOutlined,
  ShopOutlined,
  TableOutlined,
  TeamOutlined,
  UserSwitchOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { ReactNode } from "react";

export interface SidebarNavigationItem {
  title: string;
  key: string;
  url?: string;
  children?: SidebarNavigationItem[];
  icon?: ReactNode;
  label?: string;
  roles?: string[];
}

export const ROLES = {
  ADMIN: "Admin",
  AUTHENTICATED: "Authenticated",
  CONSEJERO: "Consejero",
  CONSTRUCTORA: "Constructora",
  CONVIVENCIA: "Convivencia",
  PUBLIC: "Public",
  RESIDENTE: "Residente",
  SERVICIO: "Servic<PERSON>",
  VIGILANTE: "Vigilante",
  ARRENDATARIO: "Arrendatar<PERSON>",
};

const {
  ADMIN,
  ARRENDATARIO,
  CONSEJERO,
  CONVIVENCIA,
  RESIDENTE,
  SERVICIO,
  VIGILANTE,
} = ROLES;

export const sidebarNavigation: SidebarNavigationItem[] = [
  {
    title: "Mi Perfil",
    key: "profile",
    url: "/profile",
    icon: <IdcardOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Pagos",
    key: "payments",
    url: "/payments",
    icon: <DollarCircleFilled />,
    roles: [CONSEJERO, RESIDENTE, ADMIN, ARRENDATARIO],
  },
  {
    title: "Solicitudes",
    key: "complaints",
    url: "/complaints",
    icon: <WarningOutlined />,
    roles: [CONSEJERO, RESIDENTE, ADMIN, CONVIVENCIA],
  },
  {
    title: "Noticias",
    key: "news",
    url: "/news",
    icon: <FileTextOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Reservaciones",
    key: "reservations",
    url: "/reservations",
    icon: <CalendarOutlined />,
    roles: [CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Encuestas",
    key: "questions",
    url: "/surveys",
    icon: <FormOutlined />,
    roles: [CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Alquiler o Ventas",
    key: "houses",
    url: "/houses",
    icon: <HomeOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Negocios",
    key: "bussiness",
    url: "/bussiness",
    icon: <ShopOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Mascotas",
    key: "pets",
    url: "/pets",
    icon: <BaiduOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Documentos",
    key: "documents",
    url: "/documents",
    icon: <FileTextOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Directorio",
    key: "users",
    url: "/users",
    icon: <UserSwitchOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Notificaciones",
    key: "notifications",
    url: "/notifications/history",
    icon: <BellOutlined />,
    roles: [VIGILANTE, CONSEJERO, RESIDENTE, ADMIN],
  },
  {
    title: "Chat Comunitario",
    key: "chat",
    url: "/chat",
    icon: <CommentOutlined />,
    roles: [RESIDENTE, ARRENDATARIO, CONSEJERO, ADMIN],
  },
  {
    title: "Administración",
    key: "admin",
    icon: <TableOutlined />,
    roles: [ADMIN, VIGILANTE, CONVIVENCIA, CONSEJERO, SERVICIO],
    children: [
      {
        key: "admin-users",
        roles: [ADMIN],
        title: "Gestión de Usuarios",
        url: "/admin/users",
      },
      {
        key: "admin-complaints",
        roles: [ADMIN, CONVIVENCIA, CONSEJERO],
        title: "Gestión de Solicitudes",
        url: "/admin/complaints",
        icon: <WarningOutlined />,
      },
      {
        key: "admin-warnings",
        roles: [ADMIN, CONVIVENCIA, CONSEJERO],
        title: "Gestión de Advertencias",
        url: "/admin/warnings",
        icon: <ExclamationCircleOutlined />,
      },
      {
        key: "admin-surveys",
        roles: [ADMIN, CONVIVENCIA, CONSEJERO],
        title: "Gestión de Encuestas",
        url: "/admin/surveys",
      },
      {
        key: "admin-budget",
        roles: [ADMIN],
        title: "Gestión de Cuotas",
        url: "/admin/budget",
        icon: <CalculatorOutlined />,
      },
      {
        key: "admin-payments",
        roles: [ADMIN],
        title: "Gestión de Pagos",
        url: "/admin/payments",
        icon: <DollarCircleFilled />,
      },
      {
        key: "admin-reservations",
        roles: [ADMIN, VIGILANTE],
        title: "Gestión de Reservas",
        url: "/admin/reservations",
      },
      {
        key: "admin-pool-access",
        roles: [ADMIN, SERVICIO],
        title: "Gestión de Piscina",
        url: "/admin/pool-access",
        icon: <ControlOutlined />,
      },
      {
        key: "admin-houses",
        roles: [ADMIN],
        title: "Gestión de Propiedades",
        url: "/admin/houses",
      },
      {
        key: "admin-business",
        roles: [ADMIN],
        title: "Gestión de Negocios",
        url: "/admin/business",
      },
      {
        key: "admin-pets",
        roles: [ADMIN],
        title: "Gestión de Mascotas",
        url: "/admin/pets",
      },
      {
        key: "admin-vehicles",
        roles: [VIGILANTE, ADMIN],
        title: "Gestión de Vehículos",
        url: "/admin/vehicles",
        icon: <CarOutlined />,
      },
      {
        key: "admin-visits",
        roles: [VIGILANTE, ADMIN],
        title: "Gestión de Visitas",
        url: "/admin/visits",
        icon: <TeamOutlined />,
      },
      {
        key: "admin-consents",
        roles: [ADMIN],
        title: "Gestión de Consentimientos",
        url: "/admin/consents",
        icon: <FileTextOutlined />,
      },
    ],
  },
];
