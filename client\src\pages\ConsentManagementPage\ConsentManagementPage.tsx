import React, { useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  DatePicker,
  Select,
  Input,
  Row,
  Col,
  Statistic,
  Typography,
  Tooltip,
  Modal,
  Descriptions,
} from "antd";
import {
  DownloadOutlined,
  EyeOutlined,
  FilterOutlined,
  ReloadOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import {
  useConsents,
  useExportConsents,
  useConsent,
  ConsentFilters,
} from "@app/hooks/consent/useConsent";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

export const ConsentManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState<ConsentFilters>({});
  const [selectedConsentId, setSelectedConsentId] = useState<number | null>(
    null,
  );
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // Hooks
  const { data: consentsData, isLoading, refetch } = useConsents(filters);
  const { data: selectedConsent } = useConsent(selectedConsentId || undefined);
  const exportConsents = useExportConsents();

  // Datos procesados
  const consents = consentsData?.data || [];
  const totalConsents = consentsData?.meta?.pagination?.total || 0;

  // Estadísticas
  const activeConsents = consents.filter((c: any) => c.activo).length;
  const registroConsents = consents.filter(
    (c: any) => c.tipo_consentimiento === "registro",
  ).length;

  // Handlers
  const handleFilterChange = (key: keyof ConsentFilters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setFilters((prev) => ({
        ...prev,
        fecha_desde: dates[0].toISOString(),
        fecha_hasta: dates[1].toISOString(),
      }));
    } else {
      setFilters((prev) => ({
        ...prev,
        fecha_desde: undefined,
        fecha_hasta: undefined,
      }));
    }
  };

  const handleExport = () => {
    exportConsents.mutate(filters);
  };

  const handleViewDetails = (consentId: number) => {
    setSelectedConsentId(consentId);
    setDetailModalVisible(true);
  };

  const clearFilters = () => {
    setFilters({});
  };

  // Columnas de la tabla
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "Usuario",
      key: "user",
      render: (record: any) => {
        const user = record.user;
        return user ? (
          <div>
            <div>{user.username}</div>
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {user.email}
            </Text>
          </div>
        ) : (
          <Text type="secondary">Usuario no disponible</Text>
        );
      },
    },
    {
      title: "Tipo",
      dataIndex: "tipo_consentimiento",
      key: "tipo_consentimiento",
      render: (tipo: string) => {
        const colors: Record<string, string> = {
          registro: "blue",
          marketing: "green",
          cookies: "orange",
          actualizacion_politica: "purple",
        };
        return <Tag color={colors[tipo] || "default"}>{tipo}</Tag>;
      },
    },
    {
      title: "Fecha de Aceptación",
      dataIndex: "fecha_aceptacion",
      key: "fecha_aceptacion",
      render: (date: string) => dayjs(date).format("DD/MM/YYYY HH:mm"),
      sorter: true,
    },
    {
      title: "Versión",
      dataIndex: "version_politica",
      key: "version_politica",
      width: 100,
    },
    {
      title: "Estado",
      dataIndex: "activo",
      key: "activo",
      render: (activo: boolean) => (
        <Tag color={activo ? "success" : "error"}>
          {activo ? "Activo" : "Inactivo"}
        </Tag>
      ),
    },
    {
      title: "IP",
      dataIndex: "ip_address",
      key: "ip_address",
      width: 120,
      render: (ip: string) => (
        <Text code style={{ fontSize: "11px" }}>
          {ip}
        </Text>
      ),
    },
    {
      title: "Acciones",
      key: "actions",
      width: 100,
      render: (record: any) => (
        <Space>
          <Tooltip title="Ver detalles">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      <div style={{ marginBottom: "24px" }}>
        <Title level={2}>
          <FileTextOutlined /> Gestión de Consentimientos
        </Title>
        <Text type="secondary">
          Administra y monitorea los consentimientos de protección de datos
        </Text>
      </div>

      {/* Estadísticas */}
      <Row gutter={16} style={{ marginBottom: "24px" }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Consentimientos"
              value={totalConsents}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Consentimientos Activos"
              value={activeConsents}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Consentimientos de Registro"
              value={registroConsents}
              valueStyle={{ color: "#722ed1" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tasa de Consentimiento"
              value={
                totalConsents > 0
                  ? Math.round((activeConsents / totalConsents) * 100)
                  : 0
              }
              suffix="%"
              valueStyle={{ color: "#fa8c16" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filtros */}
      <Card style={{ marginBottom: "24px" }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              placeholder="Tipo de consentimiento"
              allowClear
              style={{ width: "100%" }}
              onChange={(value) =>
                handleFilterChange("tipo_consentimiento", value)
              }
              value={filters.tipo_consentimiento}
            >
              <Option value="registro">Registro</Option>
              <Option value="marketing">Marketing</Option>
              <Option value="cookies">Cookies</Option>
              <Option value="actualizacion_politica">
                Actualización Política
              </Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Estado"
              allowClear
              style={{ width: "100%" }}
              onChange={(value) => handleFilterChange("activo", value)}
              value={filters.activo}
            >
              <Option value={true}>Activo</Option>
              <Option value={false}>Inactivo</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={["Fecha desde", "Fecha hasta"]}
              style={{ width: "100%" }}
              onChange={handleDateRangeChange}
              value={
                filters.fecha_desde && filters.fecha_hasta
                  ? [dayjs(filters.fecha_desde), dayjs(filters.fecha_hasta)]
                  : null
              }
            />
          </Col>
          <Col span={4}>
            <Input
              placeholder="ID de usuario"
              type="number"
              onChange={(e) =>
                handleFilterChange(
                  "user",
                  e.target.value ? Number(e.target.value) : undefined,
                )
              }
              value={filters.user}
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button icon={<FilterOutlined />} onClick={clearFilters}>
                Limpiar Filtros
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => refetch()}>
                Actualizar
              </Button>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleExport}
                loading={exportConsents.isPending}
              >
                Exportar
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Tabla */}
      <Card>
        <Table
          columns={columns}
          dataSource={consents}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: totalConsents,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} de ${total} consentimientos`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Modal de detalles */}
      <Modal
        title="Detalles del Consentimiento"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            Cerrar
          </Button>,
        ]}
        width={800}
      >
        {selectedConsent && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="ID">
              {selectedConsent.data?.id}
            </Descriptions.Item>
            <Descriptions.Item label="Usuario">
              {selectedConsent.data?.user?.username || "N/A"}
            </Descriptions.Item>
            <Descriptions.Item label="Email">
              {selectedConsent.data?.user?.email || "N/A"}
            </Descriptions.Item>
            <Descriptions.Item label="Tipo">
              <Tag color="blue">
                {selectedConsent.data?.tipo_consentimiento}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Fecha de Aceptación">
              {dayjs(selectedConsent.data?.fecha_aceptacion).format(
                "DD/MM/YYYY HH:mm:ss",
              )}
            </Descriptions.Item>
            <Descriptions.Item label="Versión de Política">
              {selectedConsent.data?.version_politica}
            </Descriptions.Item>
            <Descriptions.Item label="Dirección IP">
              <Text code>{selectedConsent.data?.ip_address}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="User Agent" span={2}>
              <Text code style={{ fontSize: "11px", wordBreak: "break-all" }}>
                {selectedConsent.data?.user_agent}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="Estado">
              <Tag color={selectedConsent.data?.activo ? "success" : "error"}>
                {selectedConsent.data?.activo ? "Activo" : "Inactivo"}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Fecha de Creación">
              {dayjs(selectedConsent.data?.createdAt).format(
                "DD/MM/YYYY HH:mm:ss",
              )}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};
