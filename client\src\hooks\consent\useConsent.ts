import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { notification } from "antd";

export interface ConsentData {
  user: number;
  fecha_aceptacion: string;
  ip_address?: string;
  user_agent?: string;
  version_politica: string;
  tipo_consentimiento:
    | "registro"
    | "actualizacion_politica"
    | "marketing"
    | "cookies";
  activo: boolean;
}

export interface Consent {
  id: number;
  attributes: ConsentData & {
    createdAt: string;
    updatedAt: string;
  };
}

interface StrapiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Función auxiliar para obtener la IP del cliente
const getClientIP = async (): Promise<string> => {
  try {
    const response = await fetch("https://api.ipify.org?format=json");
    const data = await response.json();
    return data.ip || "";
  } catch (error) {
    console.warn("No se pudo obtener la IP del cliente:", error);
    return "";
  }
};

// Hook para crear consentimiento
export const useCreateConsent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      consentData: Omit<ConsentData, "ip_address" | "user_agent">,
    ) => {
      const clientIP = await getClientIP();

      const fullConsentData: ConsentData = {
        ...consentData,
        ip_address: clientIP,
        user_agent: navigator.userAgent,
      };

      const { data } = await Http.post("/api/consents", {
        data: fullConsentData,
      });
      return data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas con consentimientos
      queryClient.invalidateQueries({ queryKey: ["consents"] });
    },
  });
};

// Hook para obtener consentimientos de un usuario
export const useGetUserConsents = (userId?: number) => {
  return useQuery<StrapiResponse<Consent[]>>({
    queryKey: ["consents", "user", userId],
    queryFn: async () => {
      if (!userId) throw new Error("User ID is required");

      const { data } = await Http.get(
        `/api/consents?filters[user][id][$eq]=${userId}&sort=createdAt:desc`,
      );
      return data;
    },
    enabled: !!userId,
  });
};

// Hook para obtener el último consentimiento activo de un usuario por tipo
export const useGetLatestConsentByType = (
  userId?: number,
  tipo?: ConsentData["tipo_consentimiento"],
) => {
  return useQuery<Consent | null>({
    queryKey: ["consents", "latest", userId, tipo],
    queryFn: async () => {
      if (!userId || !tipo) return null;

      const { data } = await Http.get(
        `/api/consents?filters[user][id][$eq]=${userId}&filters[tipo_consentimiento][$eq]=${tipo}&filters[activo][$eq]=true&sort=createdAt:desc&pagination[limit]=1`,
      );

      if (data.data && data.data.length > 0) {
        return data.data[0];
      }

      return null;
    },
    enabled: !!userId && !!tipo,
  });
};

// Hook para verificar si un usuario tiene consentimiento válido
export const useHasValidConsent = (
  userId?: number,
  tipo?: ConsentData["tipo_consentimiento"],
) => {
  const { data: latestConsent, ...rest } = useGetLatestConsentByType(
    userId,
    tipo,
  );

  return {
    hasValidConsent:
      latestConsent !== null &&
      latestConsent !== undefined &&
      latestConsent.attributes.activo,
    latestConsent,
    ...rest,
  };
};

// Interfaces adicionales para administración
export interface ConsentFilters {
  tipo_consentimiento?: string;
  activo?: boolean;
  user?: number;
  fecha_desde?: string;
  fecha_hasta?: string;
}

export interface ConsentAcceptData {
  tipo_consentimiento: string;
  version_politica: string;
}

// Hook para listar todos los consentimientos (admin)
export const useConsents = (filters?: ConsentFilters) => {
  return useQuery({
    queryKey: ["consents", "admin", filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters?.tipo_consentimiento) {
        params.append(
          "filters[tipo_consentimiento][$eq]",
          filters.tipo_consentimiento,
        );
      }
      if (filters?.activo !== undefined) {
        params.append("filters[activo][$eq]", filters.activo.toString());
      }
      if (filters?.user) {
        params.append("filters[user][id][$eq]", filters.user.toString());
      }
      if (filters?.fecha_desde) {
        params.append("filters[fecha_aceptacion][$gte]", filters.fecha_desde);
      }
      if (filters?.fecha_hasta) {
        params.append("filters[fecha_aceptacion][$lte]", filters.fecha_hasta);
      }

      params.append("populate", "user");
      params.append("sort", "fecha_aceptacion:desc");

      const response = await Http.get(`api/consents?${params.toString()}`);
      return response.data;
    },
  });
};

// Hook para obtener un consentimiento específico (admin)
export const useConsent = (id: number) => {
  return useQuery({
    queryKey: ["consent", id],
    queryFn: async () => {
      const response = await Http.get(`api/consents/${id}?populate=user`);
      return response.data;
    },
    enabled: !!id,
  });
};

// Hook para aceptar consentimiento (endpoint público)
export const useAcceptConsent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ConsentAcceptData) => {
      const response = await Http.post("api/consents/accept", data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["consents"] });
      notification.success({
        message: "Consentimiento aceptado",
        description: "Su consentimiento ha sido registrado correctamente",
      });
    },
    onError: (error: any) => {
      notification.error({
        message: "Error al aceptar consentimiento",
        description:
          error.response?.data?.error?.message || "Ha ocurrido un error",
      });
    },
  });
};

// Hook para exportar consentimientos (solo admin)
export const useExportConsents = () => {
  return useMutation({
    mutationFn: async (filters?: ConsentFilters) => {
      const params = new URLSearchParams();

      if (filters?.tipo_consentimiento) {
        params.append("tipo_consentimiento", filters.tipo_consentimiento);
      }
      if (filters?.activo !== undefined) {
        params.append("activo", filters.activo.toString());
      }
      if (filters?.fecha_desde) {
        params.append("fecha_desde", filters.fecha_desde);
      }
      if (filters?.fecha_hasta) {
        params.append("fecha_hasta", filters.fecha_hasta);
      }

      const response = await Http.get(
        `api/consents/export?${params.toString()}`,
        {
          responseType: "blob",
        },
      );

      // Crear y descargar el archivo
      const blob = new Blob([response.data], { type: "application/json" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `consentimientos_${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return response.data;
    },
    onSuccess: () => {
      notification.success({
        message: "Exportación completada",
        description: "Los consentimientos han sido exportados exitosamente",
      });
    },
    onError: (error: any) => {
      notification.error({
        message: "Error al exportar",
        description:
          error.response?.data?.error?.message || "Ha ocurrido un error",
      });
    },
  });
};
